import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User, Badge } from '../../types';

const initialState: User = {
  id: '',
  username: 'Guest',
  email: '',
  level: 1,
  xp: 0,
  totalGames: 0,
  badges: [],
  isGuest: true,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<Partial<User>>) => {
      return { ...state, ...action.payload };
    },
    addXP: (state, action: PayloadAction<number>) => {
      state.xp += action.payload;
      const newLevel = Math.floor(state.xp / 1000) + 1;
      if (newLevel > state.level) {
        state.level = newLevel;
      }
    },
    incrementGames: (state) => {
      state.totalGames += 1;
    },
    addBadge: (state, action: PayloadAction<Badge>) => {
      const exists = state.badges.find(b => b.id === action.payload.id);
      if (!exists) {
        state.badges.push(action.payload);
      }
    },
    logout: (state) => {
      return { ...initialState };
    },
  },
});

export const { setUser, addXP, incrementGames, addBadge, logout } = userSlice.actions;
export default userSlice.reducer;
import { Location } from '../types';

export const calculateDistance = (loc1: Location, loc2: Location): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(loc2.lat - loc1.lat);
  const dLng = toRadians(loc2.lng - loc1.lng);
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(loc1.lat)) * Math.cos(toRadians(loc2.lat)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

export const calculateScore = (distance: number): number => {
  if (distance === 0) return 5000;
  const maxDistance = 20000; // 20,000 km
  const score = Math.max(0, 5000 - (distance / maxDistance) * 5000);
  return Math.round(score);
};

export const calculateXP = (score: number, timeUsed: number): number => {
  const baseXP = Math.floor(score / 10);
  const timeBonus = Math.max(0, (120 - timeUsed) / 120 * 50);
  return Math.round(baseXP + timeBonus);
};

export const getRandomLocation = (): Location => {
  // Famous locations for demo - in production, use proper Street View coverage
  const locations: Location[] = [
    { lat: 48.8566, lng: 2.3522, country: 'France', region: 'Paris' },
    { lat: 40.7589, lng: -73.9851, country: 'USA', region: 'New York' },
    { lat: 35.6762, lng: 139.6503, country: 'Japan', region: 'Tokyo' },
    { lat: -33.8688, lng: 151.2093, country: 'Australia', region: 'Sydney' },
    { lat: 51.5074, lng: -0.1278, country: 'UK', region: 'London' },
    { lat: 55.7558, lng: 37.6176, country: 'Russia', region: 'Moscow' },
    { lat: 52.5200, lng: 13.4050, country: 'Germany', region: 'Berlin' },
    { lat: 41.9028, lng: 12.4964, country: 'Italy', region: 'Rome' },
    { lat: 39.9042, lng: 116.4074, country: 'China', region: 'Beijing' },
    { lat: -22.9068, lng: -43.1729, country: 'Brazil', region: 'Rio de Janeiro' },
  ];
  
  return locations[Math.floor(Math.random() * locations.length)];
};

export const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
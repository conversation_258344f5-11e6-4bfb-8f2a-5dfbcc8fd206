import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { GameState, Location, GameResult } from '../../types';

const initialState: GameState = {
  currentRound: 1,
  totalRounds: 5,
  currentLocation: null,
  userGuess: null,
  score: 0,
  roundScore: 0,
  timeRemaining: 120,
  gameStatus: 'waiting',
  streetViewLoaded: false,
};

const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    setCurrentLocation: (state, action: PayloadAction<Location>) => {
      state.currentLocation = action.payload;
      state.streetViewLoaded = true;
    },
    setUserGuess: (state, action: PayloadAction<Location>) => {
      state.userGuess = action.payload;
    },
    setGameStatus: (state, action: PayloadAction<GameState['gameStatus']>) => {
      state.gameStatus = action.payload;
    },
    addScore: (state, action: PayloadAction<number>) => {
      state.roundScore = action.payload;
      state.score += action.payload;
    },
    nextRound: (state) => {
      state.currentRound += 1;
      state.userGuess = null;
      state.roundScore = 0;
      state.timeRemaining = 120;
      state.streetViewLoaded = false;
      if (state.currentRound > state.totalRounds) {
        state.gameStatus = 'finished';
      } else {
        state.gameStatus = 'playing';
      }
    },
    resetGame: (state) => {
      return { ...initialState };
    },
    setTimeRemaining: (state, action: PayloadAction<number>) => {
      state.timeRemaining = action.payload;
    },
  },
});

export const {
  setCurrentLocation,
  setUserGuess,
  setGameStatus,
  addScore,
  nextRound,
  resetGame,
  setTimeRemaining,
} = gameSlice.actions;

export default gameSlice.reducer;
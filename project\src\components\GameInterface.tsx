import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import StreetView from './StreetView';
import GameMap from './GameMap';
import GameControls from './GameControls';
import { RootState } from '../store';
import { setTimeRemaining } from '../store/slices/gameSlice';

const GameInterface: React.FC = () => {
  const dispatch = useDispatch();
  const { gameStatus, timeRemaining } = useSelector((state: RootState) => state.game);
  const { isMapExpanded } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (gameStatus === 'playing' && timeRemaining > 0) {
      timer = setInterval(() => {
        dispatch(setTimeRemaining(timeRemaining - 1));
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [gameStatus, timeRemaining, dispatch]);

  return (
    <div className="h-screen bg-gradient-to-br from-gray-900 to-black pt-16">
      <div className="h-full flex">
        {/* Street View */}
        <div className="flex-1 relative">
          <StreetView />
        </div>

        {/* Sidebar */}
        {!isMapExpanded && (
          <div className="w-96 bg-gray-900/80 backdrop-blur-md border-l border-white/10 p-6 space-y-6">
            {/* Map */}
            <div className="h-64">
              <h3 className="text-white font-semibold mb-3">World Map</h3>
              <GameMap />
            </div>

            {/* Controls */}
            <GameControls />
          </div>
        )}
      </div>
    </div>
  );
};

export default GameInterface;
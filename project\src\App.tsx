import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import Navbar from './components/Navbar';
import GameInterface from './components/GameInterface';
import LoginModal from './components/LoginModal';
import ResultsModal from './components/ResultsModal';
import { socketService } from './services/socketService';

function App() {
  useEffect(() => {
    // Initialize socket connection
    socketService.connect();
    
    return () => {
      socketService.disconnect();
    };
  }, []);

  return (
    <Provider store={store}>
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <GameInterface />
        <LoginModal />
        <ResultsModal />
      </div>
    </Provider>
  );
}

export default App;
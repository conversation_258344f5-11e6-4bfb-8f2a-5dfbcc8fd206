import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Loader } from '@googlemaps/js-api-loader';
import { RootState } from '../store';
import { setCurrentLocation } from '../store/slices/gameSlice';
import { getRandomLocation } from '../utils/gameUtils';

const StreetView: React.FC = () => {
  const streetViewRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const { currentLocation, streetViewLoaded } = useSelector((state: RootState) => state.game);

  useEffect(() => {
    const initStreetView = async () => {
      if (!streetViewRef.current) return;

      // For demo purposes, we'll create a mock street view
      // In production, use actual Google Street View API
      const location = getRandomLocation();
      dispatch(setCurrentLocation(location));

      // Create a mock street view with a beautiful landscape image
      streetViewRef.current.style.backgroundImage = 
        'url(https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg?auto=compress&cs=tinysrgb&w=1600)';
      streetViewRef.current.style.backgroundSize = 'cover';
      streetViewRef.current.style.backgroundPosition = 'center';
    };

    initStreetView();
  }, [dispatch]);

  if (!streetViewLoaded) {
    return (
      <div className="w-full h-full bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p>Loading Street View...</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={streetViewRef}
      className="w-full h-full relative"
    >
      <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-2 rounded-lg">
        <p className="text-sm">Use the map to guess where you are!</p>
      </div>
    </div>
  );
};

export default StreetView;
import React, { useRef, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { MapPin, Maximize2, Minimize2 } from 'lucide-react';
import { RootState } from '../store';
import { setUserGuess } from '../store/slices/gameSlice';
import { setMapExpanded } from '../store/slices/uiSlice';
import { Location } from '../types';

const GameMap: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const { userGuess } = useSelector((state: RootState) => state.game);
  const { isMapExpanded } = useSelector((state: RootState) => state.ui);
  const [mapImage, setMapImage] = useState<string>('https://images.pexels.com/photos/355952/pexels-photo-355952.jpeg?auto=compress&cs=tinysrgb&w=1600');

  const handleMapClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Convert pixel coordinates to lat/lng (simplified for demo)
    const lat = 90 - (y / rect.height) * 180;
    const lng = (x / rect.width) * 360 - 180;
    
    const guess: Location = { lat, lng };
    dispatch(setUserGuess(guess));
  };

  const toggleMapSize = () => {
    dispatch(setMapExpanded(!isMapExpanded));
  };

  const mapClasses = isMapExpanded 
    ? "fixed inset-4 z-40 bg-gray-900 rounded-lg shadow-2xl"
    : "w-full h-full bg-gray-900 rounded-lg";

  return (
    <div className={mapClasses}>
      <div className="relative w-full h-full">
        {/* Map header */}
        <div className="absolute top-4 left-4 right-4 flex items-center justify-between z-10">
          <div className="bg-black/70 text-white px-3 py-2 rounded-lg">
            <p className="text-sm font-medium">Click to place your guess</p>
          </div>
          <button
            onClick={toggleMapSize}
            className="bg-black/70 hover:bg-black/80 text-white p-2 rounded-lg transition-colors"
          >
            {isMapExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>
        </div>

        {/* Map */}
        <div
          ref={mapRef}
          className="w-full h-full cursor-crosshair relative overflow-hidden rounded-lg"
          style={{
            backgroundImage: `url(${mapImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
          onClick={handleMapClick}
        >
          {/* User guess pin */}
          {userGuess && (
            <div
              className="absolute transform -translate-x-1/2 -translate-y-full"
              style={{
                left: `${((userGuess.lng + 180) / 360) * 100}%`,
                top: `${((90 - userGuess.lat) / 180) * 100}%`
              }}
            >
              <MapPin className="w-8 h-8 text-red-500 drop-shadow-lg" />
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full bg-black/80 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
                Your Guess
              </div>
            </div>
          )}
        </div>

        {/* Map legend */}
        <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-2 rounded-lg">
          <div className="flex items-center space-x-2 text-sm">
            <MapPin className="w-4 h-4 text-red-500" />
            <span>Your Guess</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameMap;
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  showLoginModal: boolean;
  showProfileModal: boolean;
  showResultsModal: boolean;
  isMapExpanded: boolean;
  currentTime: string;
}

const initialState: UIState = {
  showLoginModal: false,
  showProfileModal: false,
  showResultsModal: false,
  isMapExpanded: false,
  currentTime: new Date().toLocaleTimeString(),
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setShowLoginModal: (state, action: PayloadAction<boolean>) => {
      state.showLoginModal = action.payload;
    },
    setShowProfileModal: (state, action: PayloadAction<boolean>) => {
      state.showProfileModal = action.payload;
    },
    setShowResultsModal: (state, action: PayloadAction<boolean>) => {
      state.showResultsModal = action.payload;
    },
    setMapExpanded: (state, action: PayloadAction<boolean>) => {
      state.isMapExpanded = action.payload;
    },
    updateTime: (state, action: PayloadAction<string>) => {
      state.currentTime = action.payload;
    },
  },
});

export const {
  setShowLoginModal,
  setShowProfileModal,
  setShowResultsModal,
  setMapExpanded,
  updateTime,
} = uiSlice.actions;

export default uiSlice.reducer;
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Clock, User, LogIn, Trophy } from 'lucide-react';
import { RootState } from '../store';
import { setShowLoginModal, setShowProfileModal, updateTime } from '../store/slices/uiSlice';

const Navbar: React.FC = () => {
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.user);
  const { currentTime } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    const timer = setInterval(() => {
      dispatch(updateTime(new Date().toLocaleTimeString()));
    }, 1000);

    return () => clearInterval(timer);
  }, [dispatch]);

  const handleLoginClick = () => {
    dispatch(setShowLoginModal(true));
  };

  const handleProfileClick = () => {
    dispatch(setShowProfileModal(true));
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="text-2xl font-bold text-white">
              Geo<span className="text-blue-400">Quest</span>
            </div>
          </div>

          {/* Center - Level and XP */}
          <div className="flex items-center space-x-6">
            {!user.isGuest && (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2">
                  <Trophy className="w-4 h-4 text-yellow-400" />
                  <span className="text-white font-medium">Level {user.level}</span>
                </div>
                <div className="bg-white/10 rounded-full px-4 py-2">
                  <span className="text-blue-400 font-medium">{user.xp} XP</span>
                </div>
              </div>
            )}
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Real-time clock */}
            <div className="flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2">
              <Clock className="w-4 h-4 text-white" />
              <span className="text-white font-medium">{currentTime}</span>
            </div>

            {/* User section */}
            {user.isGuest ? (
              <button
                onClick={handleLoginClick}
                className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 rounded-full px-4 py-2 transition-colors"
              >
                <LogIn className="w-4 h-4 text-white" />
                <span className="text-white font-medium">Sign In</span>
              </button>
            ) : (
              <button
                onClick={handleProfileClick}
                className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 rounded-full px-4 py-2 transition-colors"
              >
                <User className="w-4 h-4 text-white" />
                <span className="text-white font-medium">{user.username}</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
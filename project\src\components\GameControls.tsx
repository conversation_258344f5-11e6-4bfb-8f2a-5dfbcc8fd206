import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Play, RotateCcw, Clock } from 'lucide-react';
import { RootState } from '../store';
import { setGameStatus, nextRound, resetGame } from '../store/slices/gameSlice';
import { setShowResultsModal } from '../store/slices/uiSlice';
import { calculateDistance, calculateScore, calculateXP, formatTime } from '../utils/gameUtils';
import { addXP, incrementGames } from '../store/slices/userSlice';

const GameControls: React.FC = () => {
  const dispatch = useDispatch();
  const { currentRound, totalRounds, userGuess, currentLocation, timeRemaining, gameStatus } = useSelector((state: RootState) => state.game);
  const { user } = useSelector((state: RootState) => state.user);

  const handleStartGame = () => {
    dispatch(resetGame());
    dispatch(setGameStatus('playing'));
  };

  const handleSubmitGuess = () => {
    if (!userGuess || !currentLocation) return;

    const distance = calculateDistance(currentLocation, userGuess);
    const score = calculateScore(distance);
    
    if (!user.isGuest) {
      const xp = calculateXP(score, 120 - timeRemaining);
      dispatch(addXP(xp));
      
      if (currentRound === totalRounds) {
        dispatch(incrementGames());
      }
    }

    dispatch(setGameStatus('results'));
    dispatch(setShowResultsModal(true));
  };

  const handleNextRound = () => {
    dispatch(nextRound());
    dispatch(setShowResultsModal(false));
  };

  const canSubmitGuess = userGuess && gameStatus === 'playing';

  return (
    <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 space-y-4">
      {/* Game info */}
      <div className="flex items-center justify-between text-white">
        <div className="flex items-center space-x-4">
          <div className="bg-white/20 rounded-lg px-3 py-2">
            <span className="text-sm font-medium">Round {currentRound}/{totalRounds}</span>
          </div>
          <div className="flex items-center space-x-2 bg-white/20 rounded-lg px-3 py-2">
            <Clock className="w-4 h-4" />
            <span className="text-sm font-medium">{formatTime(timeRemaining)}</span>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex space-x-3">
        {gameStatus === 'waiting' && (
          <button
            onClick={handleStartGame}
            className="flex-1 bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            <Play className="w-5 h-5" />
            <span>Start Game</span>
          </button>
        )}

        {gameStatus === 'playing' && (
          <button
            onClick={handleSubmitGuess}
            disabled={!canSubmitGuess}
            className={`flex-1 font-medium py-3 px-6 rounded-lg transition-colors ${
              canSubmitGuess
                ? 'bg-blue-500 hover:bg-blue-600 text-white'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            Submit Guess
          </button>
        )}

        {gameStatus === 'results' && currentRound < totalRounds && (
          <button
            onClick={handleNextRound}
            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Next Round
          </button>
        )}

        {gameStatus === 'finished' && (
          <button
            onClick={handleStartGame}
            className="flex-1 bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            <RotateCcw className="w-5 h-5" />
            <span>Play Again</span>
          </button>
        )}
      </div>

      {/* Guess info */}
      {userGuess && (
        <div className="bg-white/10 rounded-lg p-3">
          <p className="text-white text-sm">
            Guess placed at: {userGuess.lat.toFixed(4)}°, {userGuess.lng.toFixed(4)}°
          </p>
        </div>
      )}
    </div>
  );
};

export default GameControls;